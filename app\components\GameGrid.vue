<template>
  <div class="game-grid">
    <UButton
      v-for="itemId in 37"
      :key="itemId"
      @click="handleItemClick(itemId)"
      :disabled="gameStore.state.items[itemId]?.completed"
      :variant="getItemVariant(itemId)"
      :color="getItemColor(itemId)"
      size="lg"
      class="game-item-button"
      :class="getItemClasses(itemId)"
      :aria-label="`Problem ${itemId}${gameStore.state.items[itemId]?.completed ? ' - Completed' : ''}`"
    >
      <div class="relative w-full h-full flex items-center justify-center">
        <span class="item-number">{{ itemId }}</span>
        <Icon
          v-if="gameStore.state.items[itemId]?.completed"
          name="i-lucide-check-circle"
          class="absolute top-1 right-1 w-4 h-4"
        />
        <span
          v-else-if="gameStore.state.items[itemId]?.attempts > 0"
          class="absolute top-1 right-1 w-4 h-4 bg-yellow-500 text-white text-xs rounded-full flex items-center justify-center"
        >
          {{ gameStore.state.items[itemId].attempts }}
        </span>
      </div>
    </UButton>
  </div>
</template>

<script setup lang="ts">
interface Props {
  // No props needed as we're using the global store
}

interface Emits {
  (e: 'item-selected', itemId: number): void
}

const emit = defineEmits<Emits>()
const gameStore = useGameStore()

const handleItemClick = (itemId: number) => {
  if (gameStore.state.items[itemId]?.completed) {
    return // Don't allow clicking completed items
  }

  emit('item-selected', itemId)
}

const getItemVariant = (itemId: number) => {
  const item = gameStore.state.items[itemId]
  if (item?.completed) return 'solid'
  if (item?.attempts && item.attempts > 0) return 'soft'
  return 'outline'
}

const getItemColor = (itemId: number) => {
  const item = gameStore.state.items[itemId]
  if (item?.completed) return 'success'
  if (item?.attempts && item.attempts > 0) return 'warning'
  return 'primary'
}

const getItemClasses = (itemId: number) => {
  const item = gameStore.state.items[itemId]

  return {
    'completed': item?.completed,
    'attempted': (item?.attempts ?? 0) > 0 && !item?.completed,
    'available': !item?.completed
  }
}
</script>

<style scoped>
.game-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
}

.game-item-button {
  @apply aspect-square min-h-[80px] transition-all duration-200;
}

.game-item-button:not(:disabled):hover {
  @apply scale-105;
}

.game-item-button:disabled {
  @apply cursor-default;
}

.game-item-button:disabled:hover {
  @apply scale-100;
}

.item-number {
  @apply text-xl font-bold;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .game-grid {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 0.75rem;
  }

  .item-number {
    @apply text-base;
  }
}

@media (min-width: 1024px) {
  .game-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }
}
</style>